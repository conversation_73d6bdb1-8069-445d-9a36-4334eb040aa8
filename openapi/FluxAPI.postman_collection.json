{"info": {"name": "FluxAPI Collection", "description": "Complete API collection for FluxAPI - AI chat, image generation, and utilities", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "rapidApiSecret", "value": "your-secret-key", "type": "string"}], "item": [{"name": "Health & System", "item": [{"name": "<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ping", "host": ["{{baseUrl}}"], "path": ["ping"]}}}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "List Models", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/models", "host": ["{{baseUrl}}"], "path": ["v1", "models"]}}}]}, {"name": "Chat & AI", "item": [{"name": "Chat Completions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"Llama-3.3-70B-Instruct-Turbo\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! How are you?\"\n    }\n  ],\n  \"max_tokens\": 150,\n  \"temperature\": 0.7\n}"}, "url": {"raw": "{{baseUrl}}/v1/chat/completions", "host": ["{{baseUrl}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Chat Completions (Streaming)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"Llama-3.3-70B-Instruct-Turbo\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Tell me a short story\"\n    }\n  ],\n  \"max_tokens\": 300,\n  \"temperature\": 0.8,\n  \"stream\": true\n}"}, "url": {"raw": "{{baseUrl}}/v1/chat/completions", "host": ["{{baseUrl}}"], "path": ["v1", "chat", "completions"]}}}, {"name": "Custom Model Chat (GET)", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/custom/grok-3-mini?prompt=Hello&system=You are helpful&stream=false", "host": ["{{baseUrl}}"], "path": ["custom", "grok-3-mini"], "query": [{"key": "prompt", "value": "Hello"}, {"key": "system", "value": "You are helpful"}, {"key": "stream", "value": "false"}]}}}, {"name": "Custom Model Chat (POST)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "body": {"mode": "raw", "raw": "{\n  \"model\": \"grok-3-mini\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"What is 2+2?\"\n    }\n  ],\n  \"stream\": false\n}"}, "url": {"raw": "{{baseUrl}}/v1/chat/completions/custom", "host": ["{{baseUrl}}"], "path": ["v1", "chat", "completions", "custom"]}}}, {"name": "Translate Text", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/translate?text=Hello World&lang=Spanish", "host": ["{{baseUrl}}"], "path": ["translate"], "query": [{"key": "text", "value": "Hello World"}, {"key": "lang", "value": "Spanish"}]}}}]}, {"name": "Image Generation", "item": [{"name": "Create V1 (Flux)", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/create-v1?prompt=a beautiful sunset&size=1024x1024&style=Anime", "host": ["{{baseUrl}}"], "path": ["create-v1"], "query": [{"key": "prompt", "value": "a beautiful sunset"}, {"key": "size", "value": "1024x1024"}, {"key": "style", "value": "Anime"}]}}}, {"name": "Create V2 (Aspect Ratio)", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/create-v2?prompt=mountain landscape&aspect_ratio=3:2", "host": ["{{baseUrl}}"], "path": ["create-v2"], "query": [{"key": "prompt", "value": "mountain landscape"}, {"key": "aspect_ratio", "value": "3:2"}]}}}, {"name": "Create V3 (Pollinations)", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/create-v3?prompt=abstract art&width=512&height=512", "host": ["{{baseUrl}}"], "path": ["create-v3"], "query": [{"key": "prompt", "value": "abstract art"}, {"key": "width", "value": "512"}, {"key": "height", "value": "512"}]}}}, {"name": "Create V4 (Style Presets)", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/create-v4?prompt=a robot in a garden&size=1024x1024&style=cyberpunk", "host": ["{{baseUrl}}"], "path": ["create-v4"], "query": [{"key": "prompt", "value": "a robot in a garden"}, {"key": "size", "value": "1024x1024"}, {"key": "style", "value": "cyberpunk"}]}}}, {"name": "Image to Image", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/img2img?image=https://example.com/image.jpg&prompt=make it artistic&aspect_ratio=1:1", "host": ["{{baseUrl}}"], "path": ["img2img"], "query": [{"key": "image", "value": "https://example.com/image.jpg"}, {"key": "prompt", "value": "make it artistic"}, {"key": "aspect_ratio", "value": "1:1"}]}}}]}, {"name": "Utilities", "item": [{"name": "Check Username", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/check?username=johndoe", "host": ["{{baseUrl}}"], "path": ["check"], "query": [{"key": "username", "value": "johndoe"}]}}}, {"name": "Geo Detection", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/Geo-detect", "host": ["{{baseUrl}}"], "path": ["Geo-detect"]}}}, {"name": "Text to Speech", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/tts?content=Hello World&voice_id=af_bella&category=en", "host": ["{{baseUrl}}"], "path": ["tts"], "query": [{"key": "content", "value": "Hello World"}, {"key": "voice_id", "value": "af_bella"}, {"key": "category", "value": "en"}]}}}, {"name": "Generate Temp Email", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/temp-email/new", "host": ["{{baseUrl}}"], "path": ["temp-email", "new"]}}}, {"name": "Check Email Inbox", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/temp-email/inbox?secretKey=your-secret-key", "host": ["{{baseUrl}}"], "path": ["temp-email", "inbox"], "query": [{"key": "secret<PERSON>ey", "value": "your-secret-key"}]}}}, {"name": "View Email", "request": {"method": "GET", "header": [{"key": "X-RapidAPI-Proxy-Secret", "value": "{{rapidApiSecret}}"}], "url": {"raw": "{{baseUrl}}/temp-email/view?id=email-id", "host": ["{{baseUrl}}"], "path": ["temp-email", "view"], "query": [{"key": "id", "value": "email-id"}]}}}]}]}