{
  "image_generation": {
    "create_v1": {
      "request": {
        "method": "GET",
        "url": "/create-v1?prompt=a%20beautiful%20sunset&size=1024x1024&style=Anime",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "code": 0,
          "message": "Success",
          "data": {
            "api": "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
            "prompt": "a beautiful sunset",
            "size": "1024x1024",
            "style": "Anime",
            "model": "flux-1-dev",
            "images": [
              {
                "url": "https://cdn.picgenv.net/fluxai/sunset_anime_1234567890.png"
              }
            ]
          },
          "timestamp": 1234567890
        }
      }
    },
    "create_v2": {
      "request": {
        "method": "GET",
        "url": "/create-v2?prompt=mountain%20landscape&aspect_ratio=3:2",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "status": "success",
          "model": "flux shell fast",
          "aspect_ratio": "3:2",
          "api": "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
          "image_link": "https://cdn.picgenv.net/fluxai2/mountain_landscape_1234567890.png"
        }
      }
    },
    "create_v3": {
      "request": {
        "method": "GET",
        "url": "/create-v3?prompt=abstract%20art&width=512&height=512",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "headers": {
          "Content-Type": "image/png"
        },
        "body": "Binary PNG image data"
      }
    },
    "create_v4": {
      "request": {
        "method": "GET",
        "url": "/create-v4?prompt=a%20robot%20in%20a%20garden&size=1024x1024&style=cyberpunk",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "headers": {
          "Content-Type": "image/png"
        },
        "body": "Binary PNG image data"
      }
    },
    "img2img": {
      "request": {
        "method": "GET",
        "url": "/img2img?image=https://example.com/photo.jpg&prompt=make%20it%20artistic&aspect_ratio=1:1",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "status": "completed",
          "model": "kling-v1",
          "prompt": "make it artistic",
          "aspect_ratio": "1:1",
          "images": [
            {
              "url": "https://cdn.picgenv.net/fluxai/artistic_transformation_1234567890.png"
            }
          ],
          "timestamp": 1234567890
        }
      }
    }
  },
  "style_examples": {
    "available_styles_v1": [
      "Default",
      "Cyberpunk",
      "Anime",
      "Pixelart",
      "Oilpaint",
      "3D"
    ],
    "available_styles_v4": [
      "default",
      "cyberpunk",
      "anime",
      "pixelart",
      "oilpaint",
      "3d"
    ],
    "style_demonstrations": {
      "cyberpunk": {
        "prompt": "a futuristic city at night",
        "description": "Neon lights, dark atmosphere, high-tech low-life aesthetic"
      },
      "anime": {
        "prompt": "a magical girl with flowing hair",
        "description": "Vibrant colors, large eyes, dynamic poses"
      },
      "pixelart": {
        "prompt": "a retro spaceship",
        "description": "8-bit style, blocky pixels, limited color palette"
      },
      "oilpaint": {
        "prompt": "a serene landscape",
        "description": "Brush strokes, rich textures, classical painting style"
      },
      "3d": {
        "prompt": "a modern architectural building",
        "description": "Realistic rendering, depth, professional 3D modeling"
      }
    }
  },
  "size_formats": {
    "supported_sizes": [
      "512x512",
      "1024x1024",
      "1024x768",
      "768x1024",
      "1920x1080"
    ],
    "aspect_ratios": [
      "1:1",
      "2:3",
      "3:2"
    ]
  },
  "errors": {
    "missing_prompt": {
      "request": {
        "method": "GET",
        "url": "/create-v4?size=1024x1024&style=anime"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Missing required parameter: prompt"
        }
      }
    },
    "invalid_size": {
      "request": {
        "method": "GET",
        "url": "/create-v4?prompt=test&size=invalid"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Invalid size format. Use WIDTHxHEIGHT like 1024x1024"
        }
      }
    },
    "invalid_style": {
      "request": {
        "method": "GET",
        "url": "/create-v1?prompt=test&style=InvalidStyle"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Invalid style. Supported styles: Default, Cyberpunk, Anime, Pixelart, Oilpaint, 3D"
        }
      }
    }
  },
  "curl_examples": {
    "basic_generation": "curl \"http://localhost:3000/create-v4?prompt=sunset&size=1024x1024&style=anime\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "image_transformation": "curl \"http://localhost:3000/img2img?image=https://example.com/photo.jpg&prompt=make%20it%20artistic\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "pollinations_generation": "curl \"http://localhost:3000/create-v3?prompt=abstract%20art&width=512&height=512\" -H \"X-RapidAPI-Proxy-Secret: your-secret\" --output image.png",
    "flux_with_style": "curl \"http://localhost:3000/create-v1?prompt=robot%20warrior&size=1024x1024&style=Cyberpunk\" -H \"X-RapidAPI-Proxy-Secret: your-secret\""
  },
  "best_practices": {
    "prompt_guidelines": [
      "Be specific and descriptive",
      "Include style keywords for better results",
      "Mention lighting, mood, and composition",
      "Use artistic references when appropriate",
      "Keep prompts under 500 characters for best performance"
    ],
    "size_recommendations": {
      "portraits": "768x1024 or 1:1",
      "landscapes": "1024x768 or 3:2",
      "social_media": "1024x1024 (square)",
      "wallpapers": "1920x1080 (if supported)"
    },
    "style_usage": {
      "photorealistic": "Use 'default' or '3d' styles",
      "artistic": "Use 'oilpaint' or 'anime' styles",
      "retro": "Use 'pixelart' style",
      "futuristic": "Use 'cyberpunk' style"
    }
  }
}
