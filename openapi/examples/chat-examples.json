{
  "chat_completions": {
    "simple_chat": {
      "request": {
        "method": "POST",
        "url": "/v1/chat/completions",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "model": "Llama-3.3-70B-Instruct-Turbo",
          "messages": [
            {
              "role": "system",
              "content": "You are a helpful assistant."
            },
            {
              "role": "user",
              "content": "Hello! How are you?"
            }
          ],
          "max_tokens": 150,
          "temperature": 0.7
        }
      },
      "response": {
        "status": 200,
        "body": {
          "id": "chatcmpl-1234567890",
          "object": "chat.completion",
          "created": 1234567890,
          "model": "Llama-3.3-70B-Instruct-Turbo",
          "choices": [
            {
              "index": 0,
              "message": {
                "role": "assistant",
                "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
              },
              "finish_reason": "stop"
            }
          ],
          "usage": {
            "prompt_tokens": 43,
            "completion_tokens": 18,
            "total_tokens": 61
          }
        }
      }
    },
    "conversation_with_history": {
      "request": {
        "method": "POST",
        "url": "/v1/chat/completions",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "model": "Llama-3.3-70B-Instruct-Turbo",
          "messages": [
            {
              "role": "system",
              "content": "You are a helpful assistant."
            },
            {
              "role": "user",
              "content": "What's the capital of France?"
            },
            {
              "role": "assistant",
              "content": "The capital of France is Paris."
            },
            {
              "role": "user",
              "content": "What's the population of that city?"
            }
          ],
          "max_tokens": 200,
          "temperature": 0.5
        }
      },
      "response": {
        "status": 200,
        "body": {
          "id": "chatcmpl-1234567891",
          "object": "chat.completion",
          "created": 1234567891,
          "model": "Llama-3.3-70B-Instruct-Turbo",
          "choices": [
            {
              "index": 0,
              "message": {
                "role": "assistant",
                "content": "Paris has a population of approximately 2.1 million people within the city proper, and about 12.2 million in the greater Paris metropolitan area (Île-de-France region)."
              },
              "finish_reason": "stop"
            }
          ],
          "usage": {
            "prompt_tokens": 67,
            "completion_tokens": 35,
            "total_tokens": 102
          }
        }
      }
    },
    "streaming_chat": {
      "request": {
        "method": "POST",
        "url": "/v1/chat/completions",
        "headers": {
          "Content-Type": "application/json",
          "
        },
        "body": {
          "model": "Llama-3.3-70B-Instruct-Turbo",
          "messages": [
            {
              "role": "system",
              "content": "You are a creative storyteller."
            },
            {
              "role": "user",
              "content": "Tell me a short story about a robot learning to paint."
            }
          ],
          "max_tokens": 300,
          "temperature": 0.8,
          "stream": true
        }
      },
      "response": {
        "status": 200,
        "headers": {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          "Connection": "keep-alive"
        },
        "body": "data: {\"type\":\"metadata\",\"model\":\"Llama-3.3-70B-Instruct-Turbo\",\"timestamp\":\"2025-07-12T10:30:00Z\"}\n\ndata: {\"type\":\"content\",\"content\":\"Once\",\"timestamp\":\"2025-07-12T10:30:01Z\"}\n\ndata: {\"type\":\"content\",\"content\":\" upon\",\"timestamp\":\"2025-07-12T10:30:01Z\"}\n\ndata: {\"type\":\"content\",\"content\":\" a\",\"timestamp\":\"2025-07-12T10:30:01Z\"}\n\ndata: {\"type\":\"done\",\"full_content\":\"Once upon a time, there was a robot named Artie...\",\"timestamp\":\"2025-07-12T10:30:05Z\"}\n\n"
      }
    }
  },
  "custom_models": {
    "get_endpoint": {
      "request": {
        "method": "GET",
        "url": "/custom/grok-3-mini?prompt=Hello&system=You%20are%20helpful&stream=false",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "id": "custom-1234567890",
          "object": "chat.completion",
          "created": 1234567890,
          "model": "grok-3-mini",
          "choices": [
            {
              "index": 0,
              "message": {
                "role": "assistant",
                "content": "Hello! I'm here to help. What can I assist you with today?"
              },
              "finish_reason": "stop"
            }
          ],
          "nodeId": "39b6a86e-411a-4b79-8eb4-333bcb1e7e8d"
        }
      }
    },
    "post_endpoint": {
      "request": {
        "method": "POST",
        "url": "/v1/chat/completions/custom",
        "headers": {
          "Content-Type": "application/json",
          "
        },
        "body": {
          "model": "gemini-2.5-flash",
          "messages": [
            {
              "role": "system",
              "content": "You are a helpful assistant."
            },
            {
              "role": "user",
              "content": "What is 2+2?"
            }
          ],
          "stream": false
        }
      },
      "response": {
        "status": 200,
        "body": {
          "id": "chatcmpl-1234567892",
          "object": "chat.completion",
          "created": 1234567892,
          "model": "gemini-2.5-flash",
          "choices": [
            {
              "index": 0,
              "message": {
                "role": "assistant",
                "content": "2 + 2 = 4"
              },
              "finish_reason": "stop"
            }
          ],
          "usage": {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
          }
        }
      }
    }
  },
  "translation": {
    "request": {
      "method": "GET",
      "url": "/translate?text=Hello%20World&lang=Spanish",
      "headers": {
        "
      }
    },
    "response": {
      "status": 200,
      "body": {
        "original": "Hello World",
        "translation": "Hola Mundo",
        "language": "Spanish",
        "model": "Llama-3.3-70B-Instruct-Turbo",
        "timestamp": "2025-07-12T10:30:00Z"
      }
    }
  },
  "errors": {
    "missing_parameters": {
      "request": {
        "method": "POST",
        "url": "/v1/chat/completions",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "messages": [
            {
              "role": "user",
              "content": "Hello"
            }
          ]
        }
      },
      "response": {
        "status": 400,
        "body": {
          "error": {
            "message": "Missing required parameters",
            "type": "invalid_request_error",
            "param": "model",
            "code": "missing_required_parameter"
          }
        }
      }
    },
    "invalid_model": {
      "request": {
        "method": "GET",
        "url": "/custom/unsupported-model?prompt=Hello"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Model 'unsupported-model' is not supported",
          "supported_models": [
            "grok-3-mini",
            "gemini-2.5-flash",
            "gpt-4.1-nano",
            "gpt-4.1-mini"
          ],
          "example": "/custom/grok-3-mini?prompt=Hello+world&system=You+are+helpful+assistant"
        }
      }
    }
  }
}
