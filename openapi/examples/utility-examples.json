{
  "username_checker": {
    "basic_check": {
      "request": {
        "method": "GET",
        "url": "/check?username=johndo<PERSON>",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "username": "johndoe",
          "platforms": {
            "twitter": {
              "exists": true,
              "url": "https://twitter.com/johndoe"
            },
            "github": {
              "exists": false,
              "url": null
            },
            "instagram": {
              "exists": true,
              "url": "https://www.instagram.com/johndoe"
            },
            "linkedin": {
              "exists": true,
              "url": "https://www.linkedin.com/in/johndoe"
            },
            "youtube": {
              "exists": false,
              "url": null
            },
            "tiktok": {
              "exists": true,
              "url": "https://www.tiktok.com/@johndoe"
            },
            "discord": {
              "exists": false,
              "url": null
            },
            "reddit": {
              "exists": true,
              "url": "https://www.reddit.com/user/johndoe"
            },
            "medium": {
              "exists": false,
              "url": null
            },
            "twitch": {
              "exists": false,
              "url": null
            }
          },
          "timestamp": 1234567890
        }
      }
    },
    "supported_platforms": [
      "twitter",
      "instagram",
      "facebook",
      "tiktok",
      "snapchat",
      "linkedin",
      "github",
      "medium",
      "dev.to",
      "codepen",
      "discord",
      "telegram",
      "whatsapp",
      "threads",
      "youtube",
      "twitch",
      "soundcloud",
      "bandcamp",
      "gumroad",
      "ko-fi",
      "buymeacoffee",
      "producthunt",
      "steam",
      "epic-games",
      "itch.io",
      "hackernews",
      "mastodon",
      "bluesky",
      "substack",
      "patreon",
      "onlyfans",
      "reddit",
      "pinterest",
      "behance",
      "dribbble",
      "figma",
      "notion",
      "replit",
      "glitch",
      "vercel",
      "netlify"
    ]
  },
  "geolocation": {
    "basic_detection": {
      "request": {
        "method": "GET",
        "url": "/Geo-detect",
        "headers": {
          "
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          "Accept-Language": "en-US,en;q=0.9"
        }
      },
      "response": {
        "status": 200,
        "body": {
          "ip": "*************",
          "geo": {
            "city": "New York",
            "region": "New York",
            "country": "US",
            "latitude": 40.7128,
            "longitude": -74.0060,
            "timezone": "America/New_York"
          },
          "device": {
            "browser": "Chrome",
            "browserVersion": "*********",
            "os": "Windows",
            "osVersion": "10",
            "device": "unknown",
            "type": "desktop",
            "engine": "Blink"
          },
          "headers": {
            "accept-language": "en-US,en;q=0.9",
            "referer": "https://example.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
          },
          "timestamp": 1234567890
        }
      }
    }
  },
  "text_to_speech": {
    "basic_tts": {
      "request": {
        "method": "GET",
        "url": "/tts?content=Hello%20World&voice_id=af_bella&category=en",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "status": "success",
          "voice_id": "af_bella",
          "category": "en",
          "content": "Hello World",
          "audio_url": "https://cdn.picgenv.net/tts/hello_world_1234567890.mp3",
          "timestamp": 1234567890
        }
      }
    },
    "available_voices": [
      "af_bella",
      "af_sarah",
      "af_nicole",
      "af_sky",
      "af_luna",
      "af_stella"
    ],
    "available_categories": [
      "en",
      "es",
      "fr",
      "de",
      "it",
      "pt",
      "ru",
      "ja",
      "ko",
      "zh"
    ]
  },
  "temporary_email": {
    "generate_email": {
      "request": {
        "method": "GET",
        "url": "/temp-email/new",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "address": "<EMAIL>",
          "secretKey": "sk_1234567890abcdef"
        }
      }
    },
    "check_inbox": {
      "request": {
        "method": "GET",
        "url": "/temp-email/inbox?secretKey=sk_1234567890abcdef",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "body": {
          "emails": [
            {
              "id": "email-id-123",
              "from": "<EMAIL>",
              "subject": "Welcome to our service!",
              "date": "2025-07-12T10:30:00Z",
              "preview": "Thank you for signing up. Please verify your email..."
            },
            {
              "id": "email-id-124",
              "from": "<EMAIL>",
              "subject": "Password Reset Request",
              "date": "2025-07-12T11:15:00Z",
              "preview": "We received a request to reset your password..."
            }
          ]
        }
      }
    },
    "view_email": {
      "request": {
        "method": "GET",
        "url": "/temp-email/view?id=email-id-123",
        "headers": {
          "
        }
      },
      "response": {
        "status": 200,
        "headers": {
          "Content-Type": "text/html"
        },
        "body": "<!DOCTYPE html><html><head><title>Welcome Email</title></head><body><h1>Welcome!</h1><p>Thank you for signing up...</p></body></html>"
      }
    }
  },
  "errors": {
    "missing_username": {
      "request": {
        "method": "GET",
        "url": "/check"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Missing required parameter: username"
        }
      }
    },
    "missing_tts_content": {
      "request": {
        "method": "GET",
        "url": "/tts?voice_id=af_bella"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Missing required parameter: content"
        }
      }
    },
    "invalid_secret_key": {
      "request": {
        "method": "GET",
        "url": "/temp-email/inbox?secretKey=invalid"
      },
      "response": {
        "status": 400,
        "body": {
          "error": "Invalid or expired secretKey"
        }
      }
    },
    "tts_generation_failed": {
      "request": {
        "method": "GET",
        "url": "/tts?content=very%20long%20text%20that%20exceeds%20limits..."
      },
      "response": {
        "status": 500,
        "body": {
          "error": "TTS generation timed out or failed"
        }
      }
    }
  },
  "curl_examples": {
    "username_check": "curl \"http://localhost:3000/check?username=johndoe\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "geolocation": "curl \"http://localhost:3000/Geo-detect\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "text_to_speech": "curl \"http://localhost:3000/tts?content=Hello%20World&voice_id=af_bella\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "temp_email_new": "curl \"http://localhost:3000/temp-email/new\" -H \"X-RapidAPI-Proxy-Secret: your-secret\"",
    "temp_email_inbox": "curl \"http://localhost:3000/temp-email/inbox?secretKey=your-secret-key\" -H \"X-RapidAPI-Proxy-Secret: your-secret\""
  },
  "use_cases": {
    "username_checker": [
      "Social media account creation",
      "Brand name availability",
      "Username consistency across platforms",
      "Account verification",
      "Digital identity management"
    ],
    "geolocation": [
      "Content localization",
      "Regional restrictions",
      "Analytics and tracking",
      "Security monitoring",
      "Personalized experiences"
    ],
    "text_to_speech": [
      "Accessibility features",
      "Voice assistants",
      "Audio content creation",
      "Language learning",
      "Automated announcements"
    ],
    "temporary_email": [
      "Testing email workflows",
      "Avoiding spam",
      "Anonymous registrations",
      "Development and QA",
      "Privacy protection"
    ]
  },
  "rate_limits": {
    "username_checker": "20 requests per minute",
    "geolocation": "30 requests per minute",
    "text_to_speech": "10 requests per minute",
    "temporary_email": "15 requests per minute"
  }
}
