openapi: 3.0.3
info:
  title: FluxAPI
  description: |
    A comprehensive Node.js API providing AI chat completions, image generation, and utility services.
    
    ## Features
    - OpenAI-compatible chat completions with streaming support
    - Multiple AI image generation endpoints
    - Username availability checking across 40+ platforms
    - Geolocation and device detection
    - Text-to-speech conversion
    - Temporary email services
    
    ## Authentication
    No authentication required - all endpoints are publicly accessible.
    
    ## Base URL
    ```
    http://localhost:3000
    ```
  version: 1.0.0
  contact:
    name: FluxAPI Support
    url: https://github.com/FreeCode911/fluxapi-nodejs
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000
    description: Local development server
  - url: https://your-domain.com
    description: Production server

paths:
  /ping:
    get:
      tags:
        - Health & System
      summary: Health check
      description: Simple health check endpoint for basic connectivity testing
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "pong"

  /health:
    get:
      tags:
        - Health & System
      summary: System health status
      description: Detailed system health and status information
      responses:
        '200':
          description: System health information
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [healthy, degraded, unhealthy]
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-07-12T10:30:00.000Z"
                  storage:
                    type: string
                    example: "In-Memory"
                  models_available:
                    type: integer
                    example: 5

  /v1/models:
    get:
      tags:
        - Health & System
      summary: List available AI models
      description: Get information about all available AI models
      responses:
        '200':
          description: List of available models
          content:
            application/json:
              schema:
                type: object
                properties:
                  models:
                    type: array
                    items:
                      $ref: '#/components/schemas/AIModel'

  /v1/chat/completions:
    post:
      tags:
        - Chat & AI
      summary: OpenAI-compatible chat completions
      description: |
        Create a chat completion using Together AI models. Supports streaming responses.
        Compatible with OpenAI API format.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatCompletionRequest'
            examples:
              simple_chat:
                summary: Simple chat
                value:
                  model: "Llama-3.3-70B-Instruct-Turbo"
                  messages:
                    - role: "system"
                      content: "You are a helpful assistant."
                    - role: "user"
                      content: "Hello! How are you?"
                  max_tokens: 150
                  temperature: 0.7
              conversation:
                summary: Conversation with history
                value:
                  model: "Llama-3.3-70B-Instruct-Turbo"
                  messages:
                    - role: "system"
                      content: "You are a helpful assistant."
                    - role: "user"
                      content: "What's the capital of France?"
                    - role: "assistant"
                      content: "The capital of France is Paris."
                    - role: "user"
                      content: "What's the population?"
                  max_tokens: 200
                  temperature: 0.5
              streaming:
                summary: Streaming response
                value:
                  model: "Llama-3.3-70B-Instruct-Turbo"
                  messages:
                    - role: "user"
                      content: "Tell me a story"
                  max_tokens: 300
                  temperature: 0.8
                  stream: true
      responses:
        '200':
          description: Chat completion response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatCompletionResponse'
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for streaming responses
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /custom/{model}:
    get:
      tags:
        - Chat & AI
      summary: Custom model chat (Flowith API)
      description: |
        Chat with custom models using Flowith API provider.
        Supports streaming and maintains backward compatibility.
      parameters:
        - name: model
          in: path
          required: true
          schema:
            type: string
            enum: [grok-3-mini, gemini-2.5-flash, gpt-4.1-nano, gpt-4.1-mini]
          description: Model name
          example: "grok-3-mini"
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: User message
          example: "Hello, how are you?"
        - name: system
          in: query
          required: false
          schema:
            type: string
          description: System message
          example: "You are a helpful assistant."
        - name: stream
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: Enable streaming responses
      responses:
        '200':
          description: Chat response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomChatResponse'
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for streaming responses
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /v1/chat/completions/custom:
    post:
      tags:
        - Chat & AI
      summary: OpenAI-compatible custom models
      description: |
        OpenAI-compatible endpoint for custom models using Flowith API.
        Supports conversation history and streaming.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomChatRequest'
            examples:
              simple_custom_chat:
                summary: Simple custom model chat
                value:
                  model: "grok-3-mini"
                  messages:
                    - role: "system"
                      content: "You are a helpful assistant."
                    - role: "user"
                      content: "What is 2+2?"
                  stream: false
              custom_conversation:
                summary: Custom model conversation
                value:
                  model: "gemini-2.5-flash"
                  messages:
                    - role: "system"
                      content: "You are a creative storyteller."
                    - role: "user"
                      content: "Tell me a short story about a robot."
                  stream: false
                  max_tokens: 300
                  temperature: 0.8
      responses:
        '200':
          description: Chat completion response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatCompletionResponse'
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for streaming responses
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /translate:
    get:
      tags:
        - Chat & AI
      summary: Text translation
      description: Translate text using AI models
      parameters:
        - name: text
          in: query
          required: true
          schema:
            type: string
          description: Text to translate
          example: "Hello World"
        - name: lang
          in: query
          required: true
          schema:
            type: string
          description: Target language
          example: "Spanish"
      responses:
        '200':
          description: Translation result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TranslationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /create-v1:
    get:
      tags:
        - Image Generation
      summary: Flux model image generation
      description: Generate images using Flux model with style options
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: Image description
          example: "a beautiful sunset over mountains"
        - name: size
          in: query
          required: false
          schema:
            type: string
            default: "1024x1024"
          description: Image dimensions
          example: "1024x1024"
        - name: style
          in: query
          required: false
          schema:
            type: string
            enum: [Default, Cyberpunk, Anime, Pixelart, Oilpaint, 3D]
            default: "Default"
          description: Style preset
      responses:
        '200':
          description: Image generation result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImageGenerationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /create-v2:
    get:
      tags:
        - Image Generation
      summary: Aspect ratio controlled generation
      description: Generate images with specific aspect ratio control
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: Image description
        - name: aspect_ratio
          in: query
          required: true
          schema:
            type: string
            enum: ["1:1", "2:3", "3:2"]
          description: Image aspect ratio
          example: "1:1"
      responses:
        '200':
          description: Image generation result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimpleImageResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /create-v3:
    get:
      tags:
        - Image Generation
      summary: Pollinations AI generation
      description: Generate images using Pollinations AI
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: Image description
        - name: width
          in: query
          required: false
          schema:
            type: string
            default: "384"
          description: Image width
        - name: height
          in: query
          required: false
          schema:
            type: string
            default: "384"
          description: Image height
        - name: seed
          in: query
          required: false
          schema:
            type: string
          description: Random seed for reproducibility
      responses:
        '200':
          description: Generated image
          content:
            image/png:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /create-v4:
    get:
      tags:
        - Image Generation
      summary: Style preset generation
      description: Generate images with advanced style presets
      parameters:
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: Image description
        - name: size
          in: query
          required: false
          schema:
            type: string
            default: "1024x1024"
            pattern: '^\d{2,4}x\d{2,4}$'
          description: Image dimensions (WIDTHxHEIGHT)
        - name: style
          in: query
          required: false
          schema:
            type: string
            enum: [default, cyberpunk, anime, pixelart, oilpaint, 3d]
            default: "default"
          description: Style preset
      responses:
        '200':
          description: Generated image
          content:
            image/png:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /img2img:
    get:
      tags:
        - Image Generation
      summary: Image-to-image transformation
      description: Transform existing images using AI
      parameters:
        - name: image
          in: query
          required: true
          schema:
            type: string
            format: uri
          description: Source image URL
          example: "https://example.com/image.jpg"
        - name: prompt
          in: query
          required: true
          schema:
            type: string
          description: Transformation description
          example: "make it artistic"
        - name: aspect_ratio
          in: query
          required: false
          schema:
            type: string
            enum: ["1:1", "2:3", "3:2"]
            default: "1:1"
          description: Output aspect ratio
      responses:
        '200':
          description: Image transformation result
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "completed"
                  model:
                    type: string
                    example: "kling-v1"
                  prompt:
                    type: string
                    example: "make it artistic"
                  aspect_ratio:
                    type: string
                    example: "1:1"
                  images:
                    type: array
                    items:
                      type: object
                      properties:
                        url:
                          type: string
                          format: uri
                          example: "https://cdn.picgenv.net/fluxai/transformed.png"
                  timestamp:
                    type: integer
                    example: 1234567890
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /check:
    get:
      tags:
        - Utilities
      summary: Username availability checker
      description: Check username availability across 40+ social media platforms
      parameters:
        - name: username
          in: query
          required: true
          schema:
            type: string
          description: Username to check
          example: "johndoe"
      responses:
        '200':
          description: Username availability results
          content:
            application/json:
              schema:
                type: object
                properties:
                  username:
                    type: string
                    example: "johndoe"
                  platforms:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        exists:
                          type: boolean
                          description: Whether username exists on this platform
                        url:
                          type: string
                          format: uri
                          nullable: true
                          description: URL if username exists, null otherwise
                    example:
                      twitter:
                        exists: true
                        url: "https://twitter.com/johndoe"
                      github:
                        exists: false
                        url: null
                  timestamp:
                    type: integer
                    example: 1234567890
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /Geo-detect:
    get:
      tags:
        - Utilities
      summary: IP geolocation and device detection
      description: Get IP geolocation and device information from client request
      responses:
        '200':
          description: Geolocation and device information
          content:
            application/json:
              schema:
                type: object
                properties:
                  ip:
                    type: string
                    example: "***********"
                  geo:
                    type: object
                    properties:
                      city:
                        type: string
                        example: "New York"
                      region:
                        type: string
                        example: "New York"
                      country:
                        type: string
                        example: "US"
                      latitude:
                        type: number
                        example: 40.7128
                      longitude:
                        type: number
                        example: -74.0060
                      timezone:
                        type: string
                        example: "America/New_York"
                  device:
                    type: object
                    properties:
                      browser:
                        type: string
                        example: "Chrome"
                      browserVersion:
                        type: string
                        example: "91.0.4472.124"
                      os:
                        type: string
                        example: "Windows"
                      osVersion:
                        type: string
                        example: "10"
                      device:
                        type: string
                        example: "unknown"
                      type:
                        type: string
                        example: "desktop"
                      engine:
                        type: string
                        example: "Blink"
                  headers:
                    type: object
                    properties:
                      accept-language:
                        type: string
                        example: "en-US,en;q=0.9"
                      referer:
                        type: string
                        example: "https://example.com"
                      user-agent:
                        type: string
                        example: "Mozilla/5.0..."
                  timestamp:
                    type: integer
                    example: 1234567890
        '500':
          $ref: '#/components/responses/InternalServerError'

  /tts:
    get:
      tags:
        - Utilities
      summary: Text-to-speech conversion
      description: Convert text to speech audio
      parameters:
        - name: content
          in: query
          required: true
          schema:
            type: string
          description: Text to convert to speech
          example: "Hello World"
        - name: voice_id
          in: query
          required: false
          schema:
            type: string
            default: "af_bella"
          description: Voice identifier
        - name: category
          in: query
          required: false
          schema:
            type: string
            default: "en"
          description: Language category
      responses:
        '200':
          description: Text-to-speech result
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "success"
                  voice_id:
                    type: string
                    example: "af_bella"
                  category:
                    type: string
                    example: "en"
                  content:
                    type: string
                    example: "Hello World"
                  audio_url:
                    type: string
                    format: uri
                    example: "https://cdn.picgenv.net/tts/audio.mp3"
                  timestamp:
                    type: integer
                    example: 1234567890
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /temp-email/new:
    get:
      tags:
        - Utilities
      summary: Generate temporary email
      description: Generate a new temporary email address
      responses:
        '200':
          description: New temporary email generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  address:
                    type: string
                    example: "<EMAIL>"
                  secretKey:
                    type: string
                    example: "secret-key-for-access"
        '500':
          $ref: '#/components/responses/InternalServerError'

  /temp-email/inbox:
    get:
      tags:
        - Utilities
      summary: Check email inbox
      description: Check inbox for a temporary email
      parameters:
        - name: secretKey
          in: query
          required: true
          schema:
            type: string
          description: Secret key from email generation
      responses:
        '200':
          description: Email inbox contents
          content:
            application/json:
              schema:
                type: object
                properties:
                  emails:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: "email-id-123"
                        from:
                          type: string
                          example: "<EMAIL>"
                        subject:
                          type: string
                          example: "Welcome!"
                        date:
                          type: string
                          format: date-time
                          example: "2025-07-12T10:30:00Z"
                        preview:
                          type: string
                          example: "Welcome to our service..."
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /temp-email/view:
    get:
      tags:
        - Utilities
      summary: View email content
      description: View specific email content
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: string
          description: Email ID from inbox
      responses:
        '200':
          description: Email content
          content:
            text/html:
              schema:
                type: string
                description: HTML email content
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    AIModel:
      type: object
      properties:
        alias:
          type: string
          description: Short, user-friendly model name
          example: "Llama-3.3-70B-Instruct-Turbo"
        id:
          type: string
          description: Full model identifier used by the AI provider
          example: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
        description:
          type: string
          description: Human-readable model description
          example: "Meta's 70B parameter Llama 3 model (turbo version)"

    Message:
      type: object
      required:
        - role
        - content
      properties:
        role:
          type: string
          enum: [system, user, assistant]
          description: The role of the message author
          example: "user"
        content:
          type: string
          description: The content of the message
          example: "Hello! How are you?"
          minLength: 1

    ChatCompletionRequest:
      type: object
      required:
        - model
        - messages
      properties:
        model:
          type: string
          description: Model name to use for completion
          example: "Llama-3.3-70B-Instruct-Turbo"
          enum:
            - "Llama-3.3-70B-Instruct-Turbo"
            - "DeepSeek-R1-Distill-Llama-70B"
            - "EXAONE-3.5-32B-Instruct"
            - "EXAONE-Deep-32B"
            - "AFM-4.5B-Preview"
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
          description: Array of message objects
          minItems: 1
          example:
            - role: "system"
              content: "You are a helpful assistant."
            - role: "user"
              content: "Hello! How are you?"
        max_tokens:
          type: integer
          default: 512
          minimum: 1
          maximum: 4096
          description: Maximum number of tokens to generate
          example: 150
        temperature:
          type: number
          default: 0.7
          minimum: 0
          maximum: 2
          description: Sampling temperature
          example: 0.7
        stream:
          type: boolean
          default: false
          description: Enable streaming responses
          example: false
      example:
        model: "Llama-3.3-70B-Instruct-Turbo"
        messages:
          - role: "system"
            content: "You are a helpful assistant."
          - role: "user"
            content: "Hello! How are you?"
        max_tokens: 150
        temperature: 0.7
        stream: false

    CustomChatRequest:
      type: object
      required:
        - model
        - messages
      properties:
        model:
          type: string
          enum: [grok-3-mini, gemini-2.5-flash, gpt-4.1-nano, gpt-4.1-mini]
          description: Custom model name
          example: "grok-3-mini"
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
          description: Array of message objects
        stream:
          type: boolean
          default: false
          description: Enable streaming responses
        max_tokens:
          type: integer
          default: 512
          description: Maximum tokens to generate
        temperature:
          type: number
          default: 0.7
          description: Sampling temperature

    ChatCompletionResponse:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the completion
          example: "chatcmpl-1234567890"
        object:
          type: string
          enum: [chat.completion]
          description: Object type
        created:
          type: integer
          description: Unix timestamp of creation
          example: 1234567890
        model:
          type: string
          description: Model used for completion
          example: "Llama-3.3-70B-Instruct-Turbo"
        choices:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
                description: Choice index
              message:
                $ref: '#/components/schemas/Message'
              finish_reason:
                type: string
                enum: [stop, length, content_filter]
                description: Reason for completion finish
        usage:
          type: object
          properties:
            prompt_tokens:
              type: integer
              description: Number of tokens in the prompt
            completion_tokens:
              type: integer
              description: Number of tokens in the completion
            total_tokens:
              type: integer
              description: Total number of tokens

    CustomChatResponse:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier
          example: "custom-1234567890"
        object:
          type: string
          enum: [chat.completion]
        created:
          type: integer
          description: Unix timestamp
        model:
          type: string
          description: Model used
        choices:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
              message:
                $ref: '#/components/schemas/Message'
              finish_reason:
                type: string
        nodeId:
          type: string
          description: Flowith API node identifier

    TranslationResponse:
      type: object
      properties:
        original:
          type: string
          description: Original text
          example: "Hello World"
        translation:
          type: string
          description: Translated text
          example: "Hola Mundo"
        language:
          type: string
          description: Target language
          example: "Spanish"
        model:
          type: string
          description: Model used for translation
          example: "Llama-3.3-70B-Instruct-Turbo"
        timestamp:
          type: string
          format: date-time
          description: Translation timestamp

    ImageGenerationResponse:
      type: object
      properties:
        code:
          type: integer
          example: 0
        message:
          type: string
          example: "Success"
        data:
          type: object
          properties:
            api:
              type: string
              example: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style"
            prompt:
              type: string
              example: "a beautiful sunset"
            size:
              type: string
              example: "1024x1024"
            style:
              type: string
              example: "Anime"
            model:
              type: string
              example: "flux-1-dev"
            images:
              type: array
              items:
                type: object
                properties:
                  url:
                    type: string
                    format: uri
                    example: "https://cdn.picgenv.net/fluxai/image.png"
        timestamp:
          type: integer
          example: 1234567890

    SimpleImageResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        model:
          type: string
          example: "flux shell fast"
        aspect_ratio:
          type: string
          example: "1:1"
        api:
          type: string
          example: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style"
        image_link:
          type: string
          format: uri
          example: "https://cdn.picgenv.net/fluxai2/image.png"

    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            message:
              type: string
              description: Error message
            type:
              type: string
              description: Error type
            code:
              type: string
              description: Error code
            details:
              type: string
              description: Additional error details

  responses:
    BadRequest:
      description: Bad request - invalid parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error:
              message: "Missing required parameters"
              type: "invalid_request_error"
              code: "missing_required_parameter"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error:
              message: "Failed to process request"
              type: "api_error"
              code: "internal_error"

tags:
  - name: Health & System
    description: Health checks and system information
  - name: Chat & AI
    description: AI chat completions and language services
  - name: Image Generation
    description: AI image generation and transformation
  - name: Utilities
    description: Username checking, geolocation, and other utilities
