import { Application, Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";
import { Together } from "npm:together-ai";
import { v4 } from "https://deno.land/std@0.224.0/uuid/mod.ts";
import UAParser from "npm:ua-parser-js@1.0.33";


const app = new Application();
const router = new Router();
const PORT = Deno.env.get("PORT") || 3000;
const kv = await Deno.openKv(); // Deno's built-in key-value store

// RapidAPI security configuration
const RAPIDAPI_PROXY_SECRET = "dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2";

// Middleware to validate RapidAPI requests
const validateRapidAPIRequest = async (ctx, next) => {
  // Skip validation in development mode if needed
  if (Deno.env.get("DEV_MODE") === "true") {
    await next();
    return;
  }

  // Get the RapidAPI proxy secret header
  const proxySecret = ctx.request.headers.get("X-RapidAPI-Proxy-Secret");

  // Validate the proxy secret
  const isValidSecret = proxySecret === RAPIDAPI_PROXY_SECRET;

  // If secret is invalid, return 403 Forbidden
  if (!isValidSecret) {
    ctx.response.status = 403;
    ctx.response.body = { error: "Access forbidden" };
    return;
  }

  await next();
};


const together = new Together({
  apiKey: "bdceafda18ca965c9eb0a060a6d48e680a5cf85e36eac53ec1714179f78f1a81"
});

const modelMap: Record<string, { id: string; description: string }> = {
  "Llama-3.3-70B-Instruct-Turbo": {
    id: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
    description: "Meta's 70B parameter Llama 3 model (turbo version)"
  },
  "DeepSeek-R1-Distill-Llama-70B": {
    id: "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
    description: "DeepSeek's distilled version of Llama 70B"
  },
  "EXAONE-3.5-32B-Instruct": {
    id: "lgai/exaone-3-5-32b-instruct",
    description: "EXAONE"
  },
  "EXAONE-Deep-32B": {
    id: "lgai/exaone-deep-32b",
    description: "EXAONE"
  },
  "AFM-4.5B-Preview": {
    id: "arcee-ai/AFM-4.5B-Preview",
    description: "AFM"
  },
};
function generateDeviceId() {
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  const numbers = "**********";
  const randomNumbers = Array.from({ length: 8 }, () => numbers[Math.floor(Math.random() * numbers.length)]).join("");
  return `dev${randomNumbers}`;
}

function getRandomSeed() {
  return Math.floor(Math.random() * 1000000);
}

// Simple ping endpoint for health checks
router.get("/ping", async (ctx) => {
  ctx.response.status = 200;
  ctx.response.body = { message: "pong" };
});


const PLATFORMS: Record<string, (username: string) => string> = {
  twitter: (u:string)=>`https://twitter.com/${u}`,
  github: (u:string)=>`https://github.com/${u}`,
  reddit: (u:string)=>`https://www.reddit.com/user/${u}`,
  instagram: (u:string)=>`https://www.instagram.com/${u}`,
  youtube: (u:string)=>`https://www.youtube.com/@${u}`,
  facebook: (u:string)=>`https://www.facebook.com/${u}`,
  tiktok: (u:string)=>`https://www.tiktok.com/@${u}`,
  pinterest: (u:string)=>`https://www.pinterest.com/${u}`,
  twitch: (u:string)=>`https://www.twitch.tv/${u}`,
  threads: (u:string)=>`https://www.threads.net/@${u}`,
  snapchat: (u:string)=>`https://www.snapchat.com/add/${u}`,
  discord: (u:string)=>`https://discord.com/users/${u}`,
  telegram: (u:string)=>`https://t.me/${u}`,
  whatsapp: (u:string)=>`https://wa.me/${u}`,
  linkedin: (u:string)=>`https://www.linkedin.com/in/${u}`,
  mastodon: (u:string)=>`https://mastodon.social/@${u}`,
  bluesky: (u:string)=>`https://bsky.app/profile/${u}`,
  substack: (u:string)=>`https://${u}.substack.com`,
  medium: (u:string)=>`https://medium.com/@${u}`,
  devto: (u:string)=>`https://dev.to/${u}`,
  codepen: (u:string)=>`https://codepen.io/${u}`,
  replit: (u:string)=>`https://replit.com/@${u}`,
  hackernews: (u:string)=>`https://news.ycombinator.com/user?id=${u}`,
  producthunt: (u:string)=>`https://www.producthunt.com/@${u}`,
  gumroad: (u:string)=>`https://gumroad.com/${u}`,
  ko_fi: (u:string)=>`https://ko-fi.com/${u}`,
  buymeacoffee: (u:string)=>`https://www.buymeacoffee.com/${u}`,
  soundcloud: (u:string)=>`https://soundcloud.com/${u}`,
  bandcamp: (u:string)=>`https://${u}.bandcamp.com`,
  mewe: (u:string)=>`https://mewe.com/i/${u}`,
  minds: (u:string)=>`https://www.minds.com/${u}`,
  mewe: (u:string)=>`https://mewe.com/i/${u}`,
  yubo: (u:string)=>`https://yubo.live/${u}`,
  spacehey: (u:string)=>`https://spacehey.com/${u}`,
  nourl: (u:string)=>`https://noplace.com/${u}`,
  echo_space: (u:string)=>`https://echospace.app/${u}`,
  lapsen: (u:string)=>`https://lapse.com/${u}`,
  dispost: (u:string)=>`https://dispo.inc/@${u}`,
  be_real: (u:string)=>`https://bere.al/${u}`,
  artifact: (u:string)=>`https://artifact.com/u/${u}`,
  strap_circle: (u:string)=>`https://loopcircle.app/u/${u}`,
  komyun: (u:string)=>`https://komyun.app/${u}`,
  hivecast: (u:string)=>`https://hivecast.com/${u}`,
  fableworld: (u:string)=>`https://fableworld.com/${u}`,
  viim: (u:string)=>`https://viim.app/${u}`,
  zentry: (u:string)=>`https://zentry.app/${u}`,
  clubhouse: (u:string)=>`https://www.clubhouse.com/@${u}`,
  nextdoor: (u:string)=>`https://nextdoor.com/profile/${u}`,
  goodreads: (u:string)=>`https://www.goodreads.com/user/show/${u}`,
};


router.get("/check", async (ctx) => {
  const username = ctx.request.url.searchParams.get("username");
  if (!username) {
    ctx.response.status = 400;
    ctx.response.body = { error: "Missing 'username' parameter" };
    return;
  }

  const results: Record<string, { exists: boolean; url: string | null }> = {};

  await Promise.all(
    Object.entries(PLATFORMS).map(async ([platform, buildUrl]) => {
      const url = buildUrl(username);
      try {
        const res = await fetch(url, { method: "HEAD" });
        results[platform] = {
          exists: res.ok,
          url: res.ok ? url : null,
        };
      } catch {
        results[platform] = {
          exists: false,
          url: null,
        };
      }
    })
  );

  ctx.response.body = {
    username,
    platforms: results,
    timestamp: Date.now(),
  };
});


router.get("/Geo-detect", async (ctx) => {
  // 1. Client IP (behind proxies too)
  const ip = ctx.request.ip || ctx.request.headers.get("x-forwarded-for")?.split(",")[0] || "";

  // 2. Device/User-Agent parsing
  const uaString = ctx.request.headers.get("user-agent") || "";
  const parser = new UAParser(uaString);
  const uaResult = parser.getResult();
  
  // 3. Geolocation API
  let geo = {};
  try {
    const geoRes = await fetch(`https://ipapi.co/${ip}/json/`);
    geo = await geoRes.json();
  } catch {
    geo = { error: "Geo lookup failed" };
  }

  ctx.response.body = {
    ip,
    geo,
    device: {
      browser: uaResult.browser.name,
      browserVersion: uaResult.browser.version,
      os: uaResult.os.name,
      osVersion: uaResult.os.version,
      device: uaResult.device.model || "unknown",
      type: uaResult.device.type || "desktop",
      engine: uaResult.engine.name
    },
    headers: {
      "accept-language": ctx.request.headers.get("accept-language"),
      referer: ctx.request.headers.get("referer"),
      "user-agent": uaString
    },
    timestamp: Date.now()
  };
});


// =====================
// ADDED TRANSLATION ENDPOINT
// =====================
router.get("/translate", async (ctx: any) => {
  const text = ctx.request.url.searchParams.get("text");
  const lang = ctx.request.url.searchParams.get("lang");
  
  // Validate parameters
  if (!text || !lang) {
    ctx.response.status = 400;
    ctx.response.body = {
      error: "Both 'text' and 'lang' parameters are required",
      example: "/translate?text=Hello%20World&lang=Spanish"
    };
    return;
  }

  // Decode and sanitize inputs
  const decodedText = decodeURIComponent(text).slice(0, 500);
  const decodedLang = decodeURIComponent(lang).slice(0, 50);

  try {
    // Use Together AI for translation
    const response = await together.chat.completions.create({
      model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      messages: [
        {
          role: "system",
          content: `Translate the text to ${decodedLang} without explanations. Only return the translated text.`
        },
        {
          role: "user",
          content: `Translate: "${decodedText}"`
        }
      ],
      temperature: 0.3,
      max_tokens: 500,
    });

    const translation = response.choices[0]?.message?.content?.trim() || "";

    ctx.response.body = {
      original: decodedText,
      translation,
      language: decodedLang,
      model: "Llama-3.3-70B-Instruct-Turbo",
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error("Translation error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      error: "Failed to translate text",
      details: error.message
    };
  }
});

// LLM
// Middleware to clean up expired sessions
const cleanupExpiredSessions = async () => {
  const now = Date.now();
  const hourAgo = now - 3600000; // 1 hour in milliseconds
  
  for await (const entry of kv.list({ prefix: ["chat_sessions"] })) {
    if (entry.value.lastActive < hourAgo) {
      await kv.delete(["chat_history", entry.key[1] as string]);
      await kv.delete(entry.key);
    }
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupExpiredSessions, 1800000);

// GET /v1/chat/:model
// Chat endpoint with history management
router.get("/v1/chat/:model", async (ctx) => {
  const startTime = Date.now();
  const { model: modelAlias } = ctx.params;
  const modelInfo = modelMap[modelAlias];

  // Get request parameters
  const prompt = ctx.request.url.searchParams.get("prompt");
  const system = ctx.request.url.searchParams.get("system") || 
                ctx.request.url.searchParams.get("systom");
  const maxTokens = parseInt(ctx.request.url.searchParams.get("max_tokens") || "512");
  const temperature = parseFloat(ctx.request.url.searchParams.get("temperature") || "0.7");
  // No import needed - uses Web Crypto API available in Deno
  const sessionId = ctx.request.url.searchParams.get("session_id") || crypto.randomUUID();
  const newSession = ctx.request.url.searchParams.get("new_session") === "true";

  // Validate model
  if (!modelInfo) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: "Model not found",
      requested_model: modelAlias,
      available_models: Object.entries(modelMap).map(([alias, info]) => ({
        alias,
        id: info.id,
        description: info.description
      }))
    };
    return;
  }

  // Validate required parameters
  if (!prompt || !system) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: "Missing required parameters",
      required_parameters: {
        prompt: "string (the user's input message)",
        system: "string (the system message defining assistant behavior)"
      },
      optional_parameters: {
        max_tokens: "number (default: 512)",
        temperature: "number (default: 0.7)",
        session_id: "string (optional, for continuing conversations)",
        new_session: "boolean (optional, start new conversation)"
      }
    };
    return;
  }

  try {
    // Get or initialize session
    let history: Array<{
      role: "user" | "assistant" | "system";
      content: string;
      timestamp: string;
    }> = [];

    if (!newSession) {
      const historyEntry = await kv.get(["chat_history", sessionId]);
      history = historyEntry.value as typeof history || [];
    }

    // Prepare messages including history (but not old system messages)
    const messages = [
      { role: "system" as const, content: system },
      ...history.filter(msg => msg.role !== "system"),
      { role: "user" as const, content: prompt }
    ];

    // Call Together AI API
    const response = await together.chat.completions.create({
      messages,
      model: modelInfo.id,
      max_tokens: maxTokens,
      temperature: temperature
    });

    const completion = response.choices[0]?.message;
    const usage = response.usage;

    // Update history with new messages
    const newHistory = [
      ...messages,
      { 
        role: "assistant" as const, 
        content: completion?.content || "",
        timestamp: new Date().toISOString()
      }
    ];

    // Save history with expiration
    await kv.set(["chat_history", sessionId], newHistory);
    await kv.set(["chat_sessions", sessionId], {
      lastActive: Date.now(),
      createdAt: new Date().toISOString()
    });

    // Prepare response
    ctx.response.body = {
      success: true,
      session_id: sessionId,
      request: {
        model: modelAlias,
       // model_id: modelInfo.id,
        model_description: modelInfo.description,
        parameters: {
          system: system,
          prompt: prompt,
          max_tokens: maxTokens,
          temperature: temperature
        },
        timestamp: new Date().toISOString(),
        processing_time_ms: Date.now() - startTime
      },
      response: {
        content: completion?.content,
        finish_reason: response.choices[0]?.finish_reason,
        usage: {
          prompt_tokens: usage?.prompt_tokens,
          completion_tokens: usage?.completion_tokens,
          total_tokens: usage?.total_tokens
        }
      },
      history: {
        count: newHistory.length,
        expires_at: new Date(Date.now() + 3600000).toISOString() // 1 hour from now
      }
    };

  } catch (error) {
    console.error("API error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to process request",
      details: error.message,
      request_info: {
        attempted_model: modelAlias,
        parameters_provided: {
          prompt_length: prompt?.length,
          system_length: system?.length,
          max_tokens,
          temperature
        }
      }
    };
  }
});

router.get("/tts", async (ctx) => {
  const params = ctx.request.url.searchParams;
  const content = params.get("content");
  const voice_id = params.get("voice_id") || "af_bella";
  const category = params.get("category") || "en";

  if (!content) {
    ctx.response.status = 400;
    ctx.response.body = { error: "Missing required parameter: content" };
    return;
  }

  try {
    // Step 1: Initial TTS Request
    const initRes = await fetch("https://vchanger.ai/api/v1/ai-text-to-audio/audio", {
      method: "POST",
      headers: {
        "accept": "application/json, text/plain, */*",
        "content-type": "application/json",
        "origin": "https://vchanger.ai",
        "referer": "https://vchanger.ai/text-to-speech",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
      },
      body: JSON.stringify({
        content,
        voice_id,
        category
      })
    });

    const initData = await initRes.json();
    const audio_id = initData?.data?.audio_id;

    if (!audio_id) {
      ctx.response.status = 500;
      ctx.response.body = { error: "Failed to create TTS task" };
      return;
    }

    // Step 2: Poll for result
    let resultData;
    for (let i = 0; i < 10; i++) {
      await new Promise((res) => setTimeout(res, 2000));
      const pollRes = await fetch(`https://vchanger.ai/api/v1/ai-text-to-audio/audio/${audio_id}`);
      const pollData = await pollRes.json();

      if (pollData?.data?.status === "success" && pollData?.data?.audio_url) {
        resultData = pollData.data;
        break;
      }
    }

    if (!resultData?.audio_url) {
      ctx.response.status = 504;
      ctx.response.body = { error: "TTS generation timed out or failed" };
      return;
    }

    // Replace audio domain
    const modified_url = resultData.audio_url.replace(
      "https://cdn.vchanger.ai/vchanger/products/audio/",
      "https://cdn.picgenv.net/tts/"
    );

    // Respond with custom audio URL
    ctx.response.body = {
      status: "success",
      voice_id,
      category,
      content,
      audio_url: modified_url,
      timestamp: Date.now()
    };
  } catch (err) {
    console.error("TTS Error:", err);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});



// Get chat history endpoint
router.get("/v1/chat/history/:session_id", async (ctx) => {
  const { session_id } = ctx.params;
  
  try {
    const historyEntry = await kv.get(["chat_history", session_id]);
    const sessionEntry = await kv.get(["chat_sessions", session_id]);
    
    if (!historyEntry.value || !sessionEntry.value) {
      ctx.response.status = 404;
      ctx.response.body = {
        success: false,
        error: "Session not found or expired"
      };
      return;
    }

    ctx.response.body = {
      success: true,
      session_id,
      history: {
        count: (historyEntry.value as Array<unknown>).length,
        messages: historyEntry.value,
        created_at: sessionEntry.value.createdAt,
        last_active: sessionEntry.value.lastActive,
        expires_at: new Date(sessionEntry.value.lastActive + 3600000).toISOString()
      }
    };
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to retrieve history",
      details: error.message
    };
  }
});

// Clear chat history endpoint
router.delete("/v1/chat/history/:session_id", async (ctx) => {
  const { session_id } = ctx.params;
  
  try {
    await kv.delete(["chat_history", session_id]);
    await kv.delete(["chat_sessions", session_id]);
    
    ctx.response.body = {
      success: true,
      message: "Chat history cleared",
      session_id
    };
  } catch (error) {
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: "Failed to clear history",
      details: error.message
    };
  }
});

// Add a health check endpoint
router.get("/health", (ctx) => {
  ctx.response.body = { 
    status: "healthy", 
    timestamp: new Date().toISOString(),
    storage: "Deno KV",
    models_available: Object.keys(modelMap).length
  };
});
// Add a models list endpoint
router.get("/v1/models", (ctx) => {
  ctx.response.body = {
    models: Object.entries(modelMap).map(([alias, info]) => ({
      alias,
      id: info.id,
      description: info.description
    }))
  };
});


const userAgents = [
  // Windows desktop
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.4; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/145.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.5; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/146.0.0.0 Safari/537.36",

  // Mac desktop
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_8) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_7_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.2.1 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_8) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.3 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_9) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.4 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.8 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_10) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.5 Safari/605.1.15",

  // Linux desktop
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

  // iPhone
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16C101",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D39",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16E226",

  // iPad
  "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
  "Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPad; CPU OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
  "Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B95",
// iPad
"Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
"Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
"Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16C101",
"Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D39",
"Mozilla/5.0 (iPad; CPU OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16E226",

// Android phone
"Mozilla/5.0 (Linux; Android 11; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G981U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G985U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G981U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G985U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",

// Android tablet
"Mozilla/5.0 (Linux; Android 11; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T981) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T985) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T981) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T985) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",

// Windows phone
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 735) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 830) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 735) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
];

// Random sec-ch-ua generators
function getRandomSecChUa() {
  return `"Chromium";v="${Math.floor(Math.random() * 10) + 100}", "Not.A/Brand";v="8", "Google Chrome";v="1"`;
}

// Route Handler
router.get("/custom/:model", async (ctx) => {
  try {
    const model = ctx.params.model;
    const prompt = ctx.request.url.searchParams.get("prompt");
    const system = ctx.request.url.searchParams.get("system");

    if (!prompt || !model) {
      ctx.response.status = 400;
      ctx.response.body = {
        error: "Missing required parameters",
        required: ["model (in path)", "prompt (query parameter)"],
        example: "/custom/grok-3-mini?prompt=Hello+world&system=You+are+helpful+assistant"
      };
      return;
    }

    const messages = [];
    if (system) {
      messages.push({ role: "system", content: system });
    }
    messages.push({ role: "user", content: prompt });

    // Random header values
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    const response = await fetch("https://edge.flowith.net/ai/chat?mode=general", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        "origin": "https://flowith.io",
        "referer": "https://flowith.io/",
        "priority": "u=1, i",
        "sec-ch-ua": getRandomSecChUa(),
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "cross-site",
        "sec-gpc": "1",
        "user-agent": userAgent
      },
      body: JSON.stringify({
        model,
        messages,
        stream: false,
      })
    });

    const contentType = response.headers.get("content-type") || "";
    let data;

    if (contentType.includes("application/json")) {
      data = await response.json();
    } else {
      const textResponse = await response.text();

      if (textResponse.includes("<say>")) {
        const sayMatches = textResponse.match(/<say>([\s\S]*?)<\/say>/g) || [];
        const contents = sayMatches.map(m => m.replace(/<\/?say>/g, "").trim());
        const content = contents.join("\n\n").trim();

        data = {
          id: `custom-${Date.now()}`,
          object: "chat.completion",
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            message: {
              role: "assistant",
              content: content || "Response received"
            }
          }]
        };
      } else {
        data = {
          id: `custom-${Date.now()}`,
          object: "chat.completion",
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            message: {
              role: "assistant",
              content: textResponse.trim()
            }
          }]
        };
      }
    }

    ctx.response.body = data;

  } catch (error) {
    console.error("Custom model error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      error: "Failed to process request",
      details: error.message,
      troubleshooting: [
        "Try a different model",
        "Simplify your prompt",
        "Check model availability on Flowith",
        "Add system prompt for better guidance"
      ]
    };
  }
});

// API routes
router.get("/create-v4", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    let prompt = params.get("prompt");
    const size = params.get("size") || "1024x1024";
    const style = (params.get("style") || "default").toLowerCase();

    if (!prompt) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing required parameter: prompt" };
      return;
    }

    const sizeMatch = size.match(/^(\d{2,4})x(\d{2,4})$/);
    if (!sizeMatch) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Invalid size format. Use WIDTHxHEIGHT like 1024x1024" };
      return;
    }

    const width = parseInt(sizeMatch[1]);
    const height = parseInt(sizeMatch[2]);

    // Style prompt mapping
const styleMap: Record<string, string> = {
  default: "",
  cyberpunk: "ultra-detailed futuristic cyberpunk cityscape, neon lights, high-tech aesthetics, Blade Runner vibe",
  anime: "vibrant anime illustration, expressive characters, large eyes, Japanese art style, Studio Ghibli influence",
  pixelart: "8-bit retro pixel art style, low-resolution, vibrant color blocks, nostalgic video game aesthetic",
  oilpaint: "realistic classic oil painting, rich textures, Renaissance style, dramatic lighting, brushstroke details",
  "3d": "highly detailed 3D render, digital sculpting, photorealistic textures, studio lighting, cinematic angle"
};


    if (!(style in styleMap)) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Invalid style. Available: default, cyberpunk, anime, pixelart, oilpaint, 3d" };
      return;
    }

    // Append style if any
    const stylePrompt = styleMap[style];
    if (stylePrompt) prompt += `, ${stylePrompt}`;

    const seed = Math.floor(Math.random() * 1000000000);
    const encodedPrompt = encodeURIComponent(prompt);
    const imageUrl = `https://fluxwebui.com/generate/${encodedPrompt}?width=${width}&height=${height}&seed=${seed}&model=flux&nologo=true&nofeed=true`;

    const imageRes = await fetch(imageUrl, {
      headers: {
        "accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "accept-language": "en-US,en;q=0.9",
        "referer": "https://fluxwebui.com/tools/ai-image-generator",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "image",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      }
    });

    if (!imageRes.ok) {
      ctx.response.status = 500;
      ctx.response.body = { error: "Failed to generate image" };
      return;
    }

    const buffer = await imageRes.arrayBuffer();
    ctx.response.status = 200;
    ctx.response.headers.set("Content-Type", "image/png");
    ctx.response.body = new Uint8Array(buffer);
  } catch (err) {
    console.error("Error in /create-v4:", err);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});


router.get("/create-v1", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const prompt = params.get("prompt");
    const size = params.get("size") || "1024x1024";
    const style = params.get("style") || "Default";
    const device_id = generateDeviceId();
    const model = "flux-1-dev";

    if (!prompt) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing prompt parameter." };
      return;
    }

    const generationRes = await fetch(`https://api-preview.apirouter.ai/api/v1/deepimg/${model}`, {
      method: "POST",
      headers: {
        "accept": "*/*",
        "content-type": "application/json",
        "origin": "https://deepimg.ai",
        "referer": "https://deepimg.ai/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        device_id,
        prompt: `${prompt} -style ${style}`,
        size,
        n: "1",
        output_format: "png",
      }),
    });

    const result = await generationRes.json();

    if (result?.code !== 0 || !result?.data?.images?.length) {
      ctx.response.status = 500;
      ctx.response.body = {
        code: result?.code ?? 500,
        message: "Failed to generate image.",
        data: null,
      };
      return;
    }

    // Transform image URLs from middle.ihomepage.app to cdn.picgenv.net/fluxai/
    const transformedImages = result.data.images.map(img => {
      if (typeof img === 'string') {
        return img.replace('https://middle.ihomepage.app', 'https://cdn.picgenv.net/fluxai');
      } else if (img.url) {
        return {
          ...img,
          url: img.url.replace('https://middle.ihomepage.app', 'https://cdn.picgenv.net/fluxai')
        };
      }
      return img;
    });

    ctx.response.status = 200;
    ctx.response.body = {
      code: 0,
      message: "Success",
      data: {
        api: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
        prompt,
        size,
        style,
        model,
        images: transformedImages.map(img => {
          if (typeof img === 'string') {
            return { url: img };
          } else if (img.url) {
            return { url: img.url };
          }
          return img;
        })
      },
      timestamp: Math.floor(Date.now() / 1000),
    };
  } catch (error) {
    console.error("Error in create-v1 route:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      code: 500,
      message: "Internal server error.",
      data: null,
    };
  }
});

router.get("/create-v2", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const prompt = params.get("prompt");
    const aspect_ratio = params.get("aspect_ratio");
    if (!prompt || !aspect_ratio) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing required parameters: prompt or aspect_ratio( 1:1 , 2:3 , 3:2 )" };
      return;
    }

    const apiUrl = `https://1yjs1yldj7.execute-api.us-east-1.amazonaws.com/default/ai_image?prompt=${encodeURIComponent(prompt)}&aspect_ratio=${encodeURIComponent(aspect_ratio)}&link=writecream.com`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0",
      },
    });

    if (!response.ok) {
      ctx.response.status = response.status;
      ctx.response.body = { error: "AI processing failed" };
      return;
    }

    const data = await response.json();

    // Transform the image URL from dbuzz-assets.s3.amazonaws.com to cdn.picgenv.net/fluxai2/
    let imageLink = data.image_link || "";
    imageLink = imageLink.replace("https://dbuzz-assets.s3.amazonaws.com/ai_image/public", "https://cdn.picgenv.net/fluxai2");

    ctx.response.body = {
      status: data.status || "success",
      model: "flux shell fast",
      aspect_ratio,
      api: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
      image_link: imageLink
    };
  } catch (error) {
    console.error("Error in create-v2 route:", error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});

router.get("/create-v3", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const prompt = params.get("prompt");
    const width = params.get("width") || "384";
    const height = params.get("height") || "384";
    const seed = params.get("seed") || getRandomSeed();

    if (!prompt) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing required parameter: prompt" };
      return;
    }

    const apiUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=${width}&height=${height}&model=flux&seed=${seed}&nologo=true&enhance=false`;

    const response = await fetch(apiUrl);

    if (!response.ok) {
      ctx.response.status = response.status;
      ctx.response.body = { error: "AI processing failed" };
      return;
    }

    const imageBuffer = await response.arrayBuffer();

    ctx.response.status = 200;
    ctx.response.headers.set("Content-Type", "image/png");
    ctx.response.body = new Uint8Array(imageBuffer);
  } catch (error) {
    console.error("Error in create-v3 route:", error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});

router.get("/create-v1.1-text-to-video", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const prompt = params.get("prompt");
    const user_id = params.get("user_id") || "1";
    const steps = params.get("steps") || "50";

    if (!prompt) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing required parameter: prompt" };
      return;
    }

    const apiUrl = "https://shorts.multiplewords.com/mwvideos/api/text_to_video";

    const formData = new FormData();
    formData.append("user_id", user_id);
    formData.append("prompt", prompt);
    formData.append("steps", steps);

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Accept": "*/*",
        "User-Agent": "Mozilla/5.0",
      },
      body: formData,
    });

    const data = await response.json();

    if (data.status !== 1 || !data.video_url) {
      ctx.response.status = 500;
      ctx.response.body = { error: "Failed to retrieve video URL" };
      return;
    }

    console.log("Original video_url:", data.video_url);

    // Replace only the domain part dynamically
    const customVideoUrl = data.video_url.replace(
      /^https:\/\/[^/]+/,
      "https://cdn.picgenv.net"
    );

    ctx.response.body = {
      status: "success",
      video_url: customVideoUrl,
    };
  } catch (error) {
    console.error("Error in create-v1.1-text-to-video route:", error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});

router.get("/img2img", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const image = params.get("image");
    const prompt = params.get("prompt");
    const aspect_ratio = params.get("aspect_ratio") || "1:1";
    const device_id = generateDeviceId(); // Your helper function

    if (!image || !prompt) {
      ctx.response.status = 400;
      ctx.response.body = {
        error: "Missing required parameters: image and prompt",
        example: "/img2img/kling?image=https://...&prompt=a+new+style"
      };
      return;
    }

    // Submit the task
    const submitRes = await fetch("https://api-preview.apirouter.ai/api/v1/deepimg/kling", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "content-type": "application/json",
        "origin": "https://deepimg.ai",
        "referer": "https://deepimg.ai/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        image,
        prompt,
        aspect_ratio,
        device_id
      }),
    });

    const submitData = await submitRes.json();

    if (submitData.code !== 0 || !submitData.data?.task_id) {
      ctx.response.status = 500;
      ctx.response.body = {
        error: "Image-to-image task submission failed",
        details: submitData
      };
      return;
    }

    const task_id = submitData.data.task_id;
    const pollUrl = `https://api-preview.apirouter.ai/api/v1/images/query?task_id=${task_id}&model=kling-v1&device_id=${device_id}`;

    // Polling function
    const pollForResult = async (retries = 30, delay = 2000) => {
      for (let i = 0; i < retries; i++) {
        const pollRes = await fetch(pollUrl, {
          headers: {
            "accept": "*/*",
            "origin": "https://deepimg.ai",
            "referer": "https://deepimg.ai/",
            "user-agent": "Mozilla/5.0",
          },
        });

        const pollData = await pollRes.json();

        if (pollData?.data?.status === "completed" && pollData.data.success) {
          return pollData.data.images.map(img => ({
            url: img.url.replace("https://middle.ihomepage.app", "https://cdn.picgenv.net/fluxai"),
          }));
        }

        await new Promise(res => setTimeout(res, delay));
      }

      throw new Error("Image generation timed out. Try again later.");
    };

    const finalImages = await pollForResult();

    ctx.response.status = 200;
    ctx.response.body = {
      status: "completed",
      model: "kling-v1",
      prompt,
      aspect_ratio,
      images: finalImages,
      timestamp: Math.floor(Date.now() / 1000)
    };
  } catch (error) {
    console.error("Image-to-Image Kling error:", error);
    ctx.response.status = 500;
    ctx.response.body = {
      error: "Image-to-image generation failed",
      details: error.message
    };
  }
});

const domains = [
  "@AllFreeMail.net",
  "@AllWebEmails.com",
  "@EasyMailer.live",
  "@HorizonsPost.com",
  "@InboxOrigin.com",
  "@MailMagnet.co",
  "@OpenMail.pro",
  "@SolarNyx.com",
];

const TEMP_HEADERS = {
  "accept": "*/*",
  "accept-language": "en-US,en;q=0.9",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0",
  "priority": "u=1, i",
  "referer": "https://temporarymail.com/en/",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-origin",
  "sec-gpc": "1"
};

// Route 1: Generate new temporary email
router.get("/temp-email/new", async (ctx) => {
  const genRes = await fetch(
    "https://temporarymail.com/api/?action=generateRandomName&value=1",
    { headers: TEMP_HEADERS }
  );
  const { address: localPart } = await genRes.json();

  const domain = domains[Math.floor(Math.random() * domains.length)];
  const fullAddress = `${localPart}${domain}`;

  const accessRes = await fetch(
    `https://temporarymail.com/api/?action=requestEmailAccess&key=&value=${encodeURIComponent(fullAddress)}&r=${encodeURIComponent("https://duckduckgo.com/")}`,
    { headers: TEMP_HEADERS }
  );
  const accessData = await accessRes.json();

  ctx.response.body = {
    address: accessData.address,
    secretKey: accessData.secretKey
  };
});

// Route 2: Check inbox using secretKey
router.get("/temp-email/inbox", async (ctx) => {
  const key = ctx.request.url.searchParams.get("secretKey");
  if (!key) {
    ctx.response.status = 400;
    ctx.response.body = { error: "Missing secretKey parameter" };
    return;
  }

  const inboxRes = await fetch(
    `https://temporarymail.com/api/?action=checkInbox&value=${encodeURIComponent(key)}`,
    { headers: TEMP_HEADERS }
  );
  const inboxData = await inboxRes.json();
  ctx.response.body = inboxData;
});

// Route 3: View specific email content
router.get("/temp-email/view", async (ctx) => {
  const id = ctx.request.url.searchParams.get("id");
  if (!id) {
    ctx.response.status = 400;
    ctx.response.body = { error: "Missing `id` query parameter" };
    return;
  }

  const emailUrl = `https://temporarymail.com/view/?i=${encodeURIComponent(id)}&width=0`;

  const res = await fetch(emailUrl, {
    headers: {
      "user-agent": "Mozilla/5.0",
      "referer": "https://temporarymail.com/en/",
    },
  });

  if (!res.ok) {
    ctx.response.status = res.status;
    ctx.response.body = { error: "Failed to fetch email view" };
    return;
  }

  const html = await res.text();
  ctx.response.headers.set("content-type", "text/html; charset=utf-8");
  ctx.response.body = html;
});



router.get("/create", async (ctx) => {
  try {
    const params = ctx.request.url.searchParams;
    const prompt = params.get("prompt");
    const negativePrompt = params.get("negativePrompt") || "";
    const size = params.get("size");
    const style = params.get("style") || "RANDOM";

    if (!prompt || !size) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Missing required parameters: prompt and size" };
      return;
    }

    const [width, height] = size.split("x").map(Number);
    if (!width || !height) {
      ctx.response.status = 400;
      ctx.response.body = { error: "Invalid size format. Use 'widthxheight'" };
      return;
    }

    const apiUrl = "https://perchanceai.cc/api/model/predict/v4";

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "accept": "application/json, text/plain, */*",
        "content-type": "application/json",
        "origin": "https://perchanceai.cc",
        "referer": "https://perchanceai.cc/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        key: style, // Using style parameter but API expects 'key'
      }),
    });

    const data = await response.json();

    // Transform the response to the desired format
    if (data && data.code === 0 && data.data && data.data.length > 0) {
      const imageData = data.data[0];

      // Transform the image URL from r.perchanceai.cc/static/ to cdn.picgenv.net/fluxai3/
      let transformedUrl = imageData.url || "";
      transformedUrl = transformedUrl.replace("https://r.perchanceai.cc/static/", "https://cdn.picgenv.net/fluxai3/");

      ctx.response.body = {
        status: imageData.status || "DONE",
        style: imageData.style || "",
        safeState: imageData.safeState || "UNKNOWN",
        negativePrompt: imageData.negativePrompt || "",
        url: transformedUrl
      };
    } else {
      // If the response doesn't have the expected structure, return it as is
      ctx.response.body = data;
    }
  } catch (error) {
    console.error("Error in /create route:", error);
    ctx.response.status = 500;
    ctx.response.body = { error: "Internal server error" };
  }
});

// Apply CORS middleware
app.use(oakCors());

// Apply RapidAPI validation middleware
app.use(validateRapidAPIRequest);

// Apply router middleware
app.use(router.routes());
app.use(router.allowedMethods());

console.log(`Server is running on http://localhost:${PORT}`);
await app.listen({ port: +PORT });
