<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluxAPI Streaming Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .streaming {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .status {
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FluxAPI Streaming Chat Test</h1>
        
        <div class="form-group">
            <label for="endpoint">Endpoint:</label>
            <select id="endpoint">
                <option value="together">Together AI (/v1/chat/Llama-3.3-70B-Instruct-Turbo)</option>
                <option value="custom">Custom Model (/custom/grok-3-mini)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="system">System Message:</label>
            <textarea id="system" placeholder="You are a helpful assistant...">You are a helpful assistant. Respond in a friendly and informative manner.</textarea>
        </div>

        <div class="form-group">
            <label for="prompt">User Message:</label>
            <textarea id="prompt" placeholder="Enter your message here...">Tell me a short story about a robot learning to paint.</textarea>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="streaming" checked> Enable Streaming
            </label>
        </div>

        <button onclick="sendMessage()" id="sendBtn">Send Message</button>
        <button onclick="clearResponse()" id="clearBtn">Clear Response</button>

        <div class="status" id="status"></div>
        <div class="response" id="response"></div>
    </div>

    <script>
        let eventSource = null;

        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.color = isError ? '#dc3545' : '#28a745';
        }

        function clearResponse() {
            document.getElementById('response').textContent = '';
            document.getElementById('response').className = 'response';
            updateStatus('');
        }

        async function sendMessage() {
            const endpoint = document.getElementById('endpoint').value;
            const system = document.getElementById('system').value;
            const prompt = document.getElementById('prompt').value;
            const streaming = document.getElementById('streaming').checked;
            const responseDiv = document.getElementById('response');
            const sendBtn = document.getElementById('sendBtn');

            if (!prompt.trim()) {
                alert('Please enter a message');
                return;
            }

            // Disable send button
            sendBtn.disabled = true;
            clearResponse();

            // Build URL based on endpoint
            let url;
            if (endpoint === 'together') {
                url = `/v1/chat/Llama-3.3-70B-Instruct-Turbo?prompt=${encodeURIComponent(prompt)}&system=${encodeURIComponent(system)}&stream=${streaming}`;
            } else {
                url = `/custom/grok-3-mini?prompt=${encodeURIComponent(prompt)}&system=${encodeURIComponent(system)}&stream=${streaming}`;
            }

            if (streaming) {
                // Handle streaming response
                responseDiv.className = 'response streaming';
                updateStatus('Connecting to stream...');

                try {
                    eventSource = new EventSource(url);
                    let fullContent = '';

                    eventSource.onmessage = function(event) {
                        try {
                            const data = JSON.parse(event.data);
                            
                            switch(data.type) {
                                case 'metadata':
                                    updateStatus(`Connected to ${data.model || 'model'} - streaming...`);
                                    break;
                                case 'content':
                                    fullContent += data.content;
                                    responseDiv.textContent = fullContent;
                                    break;
                                case 'done':
                                    updateStatus('Stream completed successfully');
                                    eventSource.close();
                                    sendBtn.disabled = false;
                                    break;
                                case 'error':
                                    updateStatus(`Error: ${data.error}`, true);
                                    responseDiv.className = 'response error';
                                    responseDiv.textContent = `Error: ${data.error}`;
                                    eventSource.close();
                                    sendBtn.disabled = false;
                                    break;
                            }
                        } catch (e) {
                            console.error('Error parsing stream data:', e);
                        }
                    };

                    eventSource.onerror = function(event) {
                        updateStatus('Stream connection error', true);
                        responseDiv.className = 'response error';
                        responseDiv.textContent = 'Connection error occurred';
                        eventSource.close();
                        sendBtn.disabled = false;
                    };

                } catch (error) {
                    updateStatus(`Error: ${error.message}`, true);
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `Error: ${error.message}`;
                    sendBtn.disabled = false;
                }
            } else {
                // Handle regular response
                updateStatus('Sending request...');
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (response.ok) {
                        updateStatus('Response received');
                        responseDiv.textContent = JSON.stringify(data, null, 2);
                    } else {
                        updateStatus('Request failed', true);
                        responseDiv.className = 'response error';
                        responseDiv.textContent = JSON.stringify(data, null, 2);
                    }
                } catch (error) {
                    updateStatus(`Error: ${error.message}`, true);
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `Error: ${error.message}`;
                }
                
                sendBtn.disabled = false;
            }
        }

        // Close event source when page unloads
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
