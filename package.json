{"name": "fluxapi-nodejs", "version": "1.0.0", "description": "FluxAPI Node.js server with AI image generation, chat, translation, and more", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api", "ai", "image-generation", "chat", "translation", "tts", "flux", "nodejs"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "together-ai": "^0.5.0", "ua-parser-js": "^1.0.33", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/ua-parser-js": "^0.7.39", "@types/multer": "^1.4.11"}, "engines": {"node": ">=18.0.0"}}