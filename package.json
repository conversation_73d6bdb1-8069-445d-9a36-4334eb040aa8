{"name": "fluxapi-nodejs", "version": "1.0.0", "description": "FluxAPI Node.js server with AI image generation, chat, translation, and more", "main": "example.js", "scripts": {"start": "node example.js", "dev": "nodemon example.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api", "ai", "image-generation", "chat", "translation", "tts", "flux", "nodejs"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "node-fetch": "^2.7.0", "together-ai": "^0.5.0", "ua-parser-js": "^1.0.33", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}